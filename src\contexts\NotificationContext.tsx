import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { useAuth } from './auth'; // Assuming useAuth provides the current user
import { useToast } from '@/hooks/use-toast';
import { NotificationService, AppNotification } from '@/services/notificationService';

// AppNotification interface is now imported from NotificationService

interface NotificationContextType {
  notifications: AppNotification[];
  unreadCount: number;
  loading: boolean;
  fetchNotifications: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider = ({ children }: { children: ReactNode }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [notifications, setNotifications] = useState<AppNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [subscription, setSubscription] = useState<{ unsubscribe: () => void } | null>(null);

  const fetchNotifications = useCallback(async () => {
    if (!user?.id) return;
    setLoading(true);
    try {
      const { notifications: fetchedNotifications, unreadCount: fetchedUnreadCount } = 
        await NotificationService.fetchNotifications(user.id);
      
      setNotifications(fetchedNotifications);
      setUnreadCount(fetchedUnreadCount);
    } catch (error: any) {
      console.error('Error fetching notifications:', error);
      
      // If service role key is missing, just don't show notifications
      if (error.message?.includes('Service role key not configured')) {
        console.warn('Notifications disabled: Service role key not configured');
        setNotifications([]);
        setUnreadCount(0);
        // Don't show toast for missing service role key
        return;
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({ 
        title: 'Error', 
        description: `Could not fetch notifications: ${errorMessage}`, 
        variant: 'destructive' 
      });
    } finally {
      setLoading(false);
    }
  }, [user?.id, toast]);

  useEffect(() => {
    if (user?.id) {
      fetchNotifications();
    } else {
      // Clear notifications if user logs out
      setNotifications([]);
      setUnreadCount(0);
    }
  }, [user?.id, fetchNotifications]);

  // Real-time subscription for new notifications
  useEffect(() => {
    if (subscription) {
      subscription.unsubscribe();
      setSubscription(null);
    }

    if (user?.id) {
      const newSubscription = NotificationService.subscribeToNotifications(
        user.id,
        (newNotification) => {
          setNotifications(prev => [newNotification, ...prev].slice(0, 50)); // Keep list to 50
          if (!newNotification.is_read) {
            setUnreadCount(prev => prev + 1);
          }
          toast({
            title: newNotification.content.story_title ? `Update: ${newNotification.content.story_title}` : 'New Notification',
            description: newNotification.content.message,
            duration: 5000,
          });
        }
      );
      setSubscription(newSubscription);
    }

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [user?.id, toast]); // Removed subscription from dependencies to avoid infinite loop

  const markAsRead = async (notificationId: string) => {
    if (!user?.id) return;
    try {
      const updatedNotification = await NotificationService.markAsRead(notificationId, user.id);
      if (updatedNotification) {
        setNotifications(prev => prev.map(n => n.id === notificationId ? updatedNotification : n));
        setUnreadCount(prev => Math.max(0, prev - 1)); // Decrement if it was unread
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast({ title: 'Error', description: 'Could not mark notification as read.', variant: 'destructive' });
    }
  };

  const markAllAsRead = async () => {
    if (!user?.id || unreadCount === 0) return;
    try {
      await NotificationService.markAllAsRead(user.id);
      // Update local state optimistically
      setNotifications(prev => prev.map(n => ({ ...n, is_read: true, read_at: new Date().toISOString() })));
      setUnreadCount(0);
      toast({ title: 'Success', description: 'All notifications marked as read.' });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast({ title: 'Error', description: 'Could not mark all notifications as read.', variant: 'destructive' });
    }
  };

  return (
    <NotificationContext.Provider value={{ notifications, unreadCount, loading, fetchNotifications, markAsRead, markAllAsRead }}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}; 