-- Add display_name and terms_accepted columns to user_profiles table
ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS display_name TEXT,
ADD COLUMN IF NOT EXISTS terms_accepted BOOLEAN DEFAULT false;

-- Optionally, set display_name to username for existing users
UPDATE public.user_profiles 
SET display_name = username 
WHERE display_name IS NULL;

-- Add comments for the new columns
COMMENT ON COLUMN public.user_profiles.display_name IS 'User-visible display name, can be different from username';
COMMENT ON COLUMN public.user_profiles.terms_accepted IS 'Whether the user has accepted the terms of service';
