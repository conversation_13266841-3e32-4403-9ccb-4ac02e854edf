import React, { useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { Loader2, ShieldAlert } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import TermsAndConditions from "@/components/legal/TermsAndConditions";

interface TermsAcceptanceProps {
  onComplete: () => void;
  onCancel: () => void;
}

const TermsAcceptance: React.FC<TermsAcceptanceProps> = ({
  onComplete,
  onCancel,
}) => {
  const [accepted, setAccepted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { updateProfile, loadUserProfile, user } = useAuth();
  const { toast } = useToast();

  const handleAcceptTerms = async () => {
    if (!accepted) {
      toast({
        title: "Terms required",
        description: "You must accept the terms to continue",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Update user profile to indicate terms acceptance
      await updateProfile({ termsAccepted: true });

      // Reload the user profile to ensure the main context gets the update
      if (user?.id) {
        await loadUserProfile(user.id);
      }

      toast({
        title: "Terms accepted",
        description: "Welcome to One Word Story!",
      });

      onComplete();
    } catch (error) {
      console.error("Error accepting terms:", error);
      toast({
        title: "Error",
        description: "Failed to save your acceptance. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDecline = () => {
    toast({
      title: "Terms declined",
      description: "You must accept the terms to use this application",
      variant: "destructive",
    });
    onCancel();
  };

  return (
    <Card className="w-full max-w-lg shadow-lg border-literary-gold/20">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShieldAlert className="h-5 w-5 text-literary-burgundy" />
          <span>Terms and Conditions</span>
        </CardTitle>
        <CardDescription>
          Please review and accept the terms and conditions to continue
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <TermsAndConditions />

        <div className="flex items-start space-x-2 pt-4">
          <Checkbox
            id="terms"
            checked={accepted}
            onCheckedChange={(checked) => setAccepted(checked === true)}
          />
          <label
            htmlFor="terms"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            I have read and agree to the Terms and Conditions
          </label>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button variant="ghost" onClick={handleDecline} disabled={isSubmitting}>
          Decline
        </Button>

        <Button
          onClick={handleAcceptTerms}
          disabled={!accepted || isSubmitting}
          className="bg-literary-burgundy hover:bg-opacity-90"
        >
          {isSubmitting ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : null}
          I Accept
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TermsAcceptance;
