import React, { createContext, useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { AuthContextType, User, SystemMaintenanceState } from "./types";
import { useSystemMaintenance } from "@/hooks/use-system-maintenance";
import { useUserProfile } from "@/hooks/use-user-profile";
import { signIn, signUp, signOut, resetPassword, getUser } from "@/services/auth";
import { Session } from "@supabase/supabase-js";
import { supabase } from "@/lib/supabase";
import { usePostHogAuth } from "@/hooks/usePostHog";
import { trackUserEvent } from "@/lib/posthog";

// Create the context with a default value of undefined
export const AuthContext = createContext<AuthContextType | undefined>(
  undefined,
);

// Create the AuthProvider component
interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const navigate = useNavigate();

  // Use our custom hooks
  const { systemMaintenance, setSystemMaintenance, loadMaintenanceSettings } =
    useSystemMaintenance();
  const {
    loadUserProfile,
    updateProfile,
    updateAchievements,
    addBadge,
    isAdmin,
  } = useUserProfile();

  // Function to create user profile for new OAuth users
  const createUserProfile = useCallback(async (authUser: any) => {
    try {
      const { error } = await supabase
        .from("user_profiles")
        .insert({
          id: authUser.id,
          email: authUser.email,
          username: authUser.user_metadata?.full_name || authUser.email?.split('@')[0] || '',
          avatar_url: authUser.user_metadata?.avatar_url || null,
          terms_accepted: false, // New users haven't accepted terms yet
          is_admin: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (error) {
        console.error("Error creating user profile:", error);
        throw error;
      }

      // Load the newly created profile
      await loadUserProfile(authUser.id);
    } catch (error) {
      console.error("Failed to create user profile:", error);
      throw error;
    }
  }, [loadUserProfile]);

  // PostHog integration for user tracking
  usePostHogAuth(user);

  // Load Supabase user/session on mount
  useEffect(() => {
    const fetchUserAndSession = async () => {
      setIsLoading(true);
      try {
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          setUser(null);
          setSession(null);
          setIsAuthenticated(false);
        } else if (sessionData?.session?.user) {
          setUser({
            id: sessionData.session.user.id,
            email: sessionData.session.user.email,
            username: sessionData.session.user.user_metadata?.username || "",
            tier: "free",
            credits: 0,
            createdAt: sessionData.session.user.created_at,
            updatedAt: sessionData.session.user.updated_at,
            termsAccepted: false, // Default to false, will be updated by loadUserProfile
          });
          setSession(sessionData.session);
          setIsAuthenticated(true);
          loadUserProfile(sessionData.session.user.id);
        } else {
          setUser(null);
          setSession(null);
          setIsAuthenticated(false);
        }
      } catch (err: any) {
        setError(err);
        setUser(null);
        setSession(null);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };
    fetchUserAndSession();

    // Listen for auth state changes
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          // Set initial user state with termsAccepted as false by default
          setUser({
            id: session.user.id,
            email: session.user.email,
            username: session.user.user_metadata?.username || "",
            tier: "free",
            credits: 0,
            createdAt: session.user.created_at,
            updatedAt: session.user.updated_at,
            termsAccepted: false, // Default to false, will be updated by loadUserProfile
          });
          setSession(session);
          setIsAuthenticated(true);

          // Load user profile and create one if it doesn't exist
          try {
            await loadUserProfile(session.user.id);
          } catch (error) {
            console.error("Error loading user profile:", error);
            // If profile doesn't exist, create one for new OAuth users
            if (event === 'SIGNED_IN' && session.user.app_metadata?.provider === 'google') {
              try {
                await createUserProfile(session.user);
              } catch (createError) {
                console.error("Error creating user profile:", createError);
              }
            }
          }
        } else {
          setUser(null);
          setSession(null);
          setIsAuthenticated(false);
        }
        setIsLoading(false);
      }
    );

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [loadUserProfile, createUserProfile]);

  useEffect(() => {
    loadMaintenanceSettings();
  }, [loadMaintenanceSettings]);

  // Auth actions
  const handleSignIn = useCallback(async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await signIn(email, password);
      if (error) throw error;
      setSession(data.session ?? null);
      setIsAuthenticated(true);
      if (data.user) {
        loadUserProfile(data.user.id);
        // Track login event
        trackUserEvent.userLoggedIn(data.user.id, 'email_password', {
          user_email: data.user.email,
          login_timestamp: new Date().toISOString(),
        });
      }
    } catch (err: any) {
      setError(err);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  }, [loadUserProfile]);

  const handleSignUp = useCallback(async (email: string, password: string, username: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await signUp(email, password);
      if (error) throw error;
      setSession(data.session ?? null);
      setIsAuthenticated(true);
      if (data.user) {
        await updateProfile({ id: data.user.id, username });
        loadUserProfile(data.user.id);
        // Track registration event
        trackUserEvent.userRegistered(data.user.id, 'email_password', {
          user_email: data.user.email,
          username: username,
          registration_timestamp: new Date().toISOString(),
        });
      }
    } catch (err: any) {
      setError(err);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  }, [updateProfile, loadUserProfile]);

  const handleRegister = useCallback(async (username: string, email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      const { data, error } = await signUp(email, password);
      if (error) throw error;
      setSession(data.session ?? null);
      setIsAuthenticated(true);
      if (data.user) {
        await updateProfile({ id: data.user.id, username });
        loadUserProfile(data.user.id);
        // Track registration event
        trackUserEvent.userRegistered(data.user.id, 'email_password', {
          user_email: data.user.email,
          username: username,
          registration_timestamp: new Date().toISOString(),
        });
      }
      return true;
    } catch (err: any) {
      setError(err);
      setIsAuthenticated(false);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [updateProfile, loadUserProfile]);

  const handleSignOut = useCallback(async () => {
    setIsLoading(true);
    try {
      // Track logout before signing out
      if (user) {
        trackUserEvent.userLoggedOut({
          logout_timestamp: new Date().toISOString(),
          session_duration: Date.now() - (new Date(user.createdAt).getTime()),
        });
      }
      await signOut();
      setUser(null);
      setSession(null);
      setIsAuthenticated(false);
    } catch (err: any) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  const handleResetPassword = useCallback(async (email: string) => {
    setIsLoading(true);
    try {
      await resetPassword(email);
    } catch (err: any) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const value: AuthContextType = {
    user,
    session,
    isAuthenticated,
    loading: isLoading,
    error,
    signIn: handleSignIn,
    signUp: handleSignUp,
    signOut: handleSignOut,
    resetPassword: handleResetPassword,
    updateProfile,
    loadUserProfile,
    handleAuthStateChange: async () => {}, // Not needed for Supabase
    handleError: setError,
    systemMaintenance,
    setSystemMaintenance,
    isAdmin,
    login: handleSignIn,
    loginWithGoogle: async () => {
      setIsLoading(true);
      try {
        const { data, error } = await supabase.auth.signInWithOAuth({
          provider: 'google',
          options: {
            redirectTo: `${window.location.origin}/dashboard`
          }
        });
        if (error) throw error;
        // The redirect will happen automatically
        return data;
      } catch (err: any) {
        setError(err);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    logout: handleSignOut,
    register: handleRegister,
    sendOTP: async () => false,
    verifyOTP: async () => false,
    resendOTP: async () => false,
    updateAchievements,
    addBadge,
    mfaResolver: null,
    setMFAResolver: () => {},
    isMFAEnabled: () => false,
    completeMFASetup: async () => false,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
