import React, { createContext, useState, useContext, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/auth";

interface AdSettings {
  showAds: boolean;
  adDensity: "low" | "medium" | "high";
}

export interface AdsContextType {
  adSettings: AdSettings;
  setAdSettings: (settings: AdSettings) => void;
  isAdFree: boolean;
  // Add the missing properties
  isAdFreeUser: boolean;
  toggleAdFreeStatus: () => Promise<void>;
  shouldShowAd: (placement: string) => boolean;
}

export type AdPlacement =
  | "sidebar"
  | "header"
  | "footer"
  | "content"
  | "story-end";

const defaultAdSettings: AdSettings = {
  showAds: true,
  adDensity: "medium",
};

export const AdsContext = createContext<AdsContextType>({
  adSettings: defaultAdSettings,
  setAdSettings: () => {},
  isAdFree: false,
  isAdFreeUser: false,
  toggleAdFreeStatus: async () => {},
  shouldShowAd: () => true,
});

export const AdsProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [adSettings, setAdSettings] = useState<AdSettings>(defaultAdSettings);
  const [isAdFree, setIsAdFree] = useState(false);
  const { user, isAuthenticated } = useAuth();
  const location = useLocation();

  // Check if user has ad-free subscription
  useEffect(() => {
    const checkAdFreeStatus = async () => {
      if (isAuthenticated && user) {
        // This would be an API call in a real app
        const userTier = user.tier || "free";
        const hasAdFree = [
          "wordsmith",
          "storyteller",
          "authors-guild",
        ].includes(userTier);
        setIsAdFree(hasAdFree);

        if (hasAdFree) {
          setAdSettings((prev) => ({ ...prev, showAds: false }));
        }
      } else {
        setIsAdFree(false);
        setAdSettings((prev) => ({ ...prev, showAds: true }));
      }
    };

    checkAdFreeStatus();
  }, [user, isAuthenticated]);

  // Disable ads on certain pages
  useEffect(() => {
    const noAdPages = [
      "/login",
      "/register",
      "/subscription",
      "/ad-free",
      "/payment",
    ];
    const shouldShowAds = !noAdPages.includes(location.pathname);

    setAdSettings((prev) => ({
      ...prev,
      showAds: shouldShowAds && !isAdFree,
    }));
  }, [location.pathname, isAdFree]);

  // Dynamically load Google AdSense script once.
  useEffect(() => {
    const clientId = import.meta.env.VITE_ADSENSE_CLIENT_ID;
    console.log('🔧 AdsContext: Loading AdSense script with client ID:', clientId);
    
    if (clientId && typeof document !== "undefined") {
      // Check if script already exists
      const existingScript = document.querySelector(`script[src*="adsbygoogle.js"]`);
      if (existingScript) {
        console.log('✅ AdSense script already loaded');
        return;
      }
      
      const script = document.createElement("script");
      script.async = true;
      script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${clientId}`;
      script.crossOrigin = "anonymous";
      
      script.onload = () => {
        console.log('✅ AdSense script loaded successfully');
      };
      
      script.onerror = (error) => {
        console.error('❌ AdSense script failed to load:', error);
      };
      
      document.head.appendChild(script);
    } else {
      console.warn('⚠️ AdSense client ID not found in environment variables');
    }
  }, []);

  // Add the missing functions
  const shouldShowAd = (placement: string) => {
    if (!adSettings.showAds || isAdFree) return false;

    // You can add logic here to control which ad placements to show
    // based on user's ad density preference
    if (adSettings.adDensity === "low" && placement !== "sidebar") {
      return false;
    }

    return true;
  };

  // Add the function to toggle ad free status
  const toggleAdFreeStatus = async () => {
    setIsAdFree((prev) => !prev);
    setAdSettings((prev) => ({ ...prev, showAds: !isAdFree }));
    return Promise.resolve();
  };

  return (
    <AdsContext.Provider
      value={{
        adSettings,
        setAdSettings,
        isAdFree,
        isAdFreeUser: isAdFree,
        toggleAdFreeStatus,
        shouldShowAd,
      }}
    >
      {children}
    </AdsContext.Provider>
  );
};

export const useAds = () => useContext(AdsContext);
