# Firebase Authentication Migration Guide

## Overview

This application has been migrated from Supabase Authentication to Firebase Authentication while maintaining Supabase as the database layer. This document outlines the changes made and important considerations for developers.

## Architecture Changes

### Before (Supabase Auth)
- Authentication: Supabase Auth
- Database: Supabase (PostgreSQL)
- User IDs: UUIDs (e.g., `550e8400-e29b-41d4-a716-************`)
- RLS Policies: Based on `auth.uid()`

### After (Firebase Auth)
- Authentication: Firebase Auth
- Database: Supabase (PostgreSQL)
- User IDs: Alphanumeric strings (e.g., `bplINgCWBwNxoUg8L7EVewg87gU2`)
- RLS Policies: Service role access (authorization handled in application layer)

## Key Changes

### 1. Database Schema Changes

All user ID columns have been converted from UUID to TEXT type to accommodate Firebase user IDs:

- `user_profiles.id`
- `notifications.user_id`
- `notifications.actor_id`
- `stories.author_id`
- `contributions.user_id`
- Any other tables with user references

### 2. Row Level Security (RLS)

- All original RLS policies have been replaced with service role policies
- Authorization is now handled at the application level using Firebase Auth
- The application uses the Supabase service role key to bypass RLS

### 3. Authentication Context

The application uses `FirebaseAuthContext` instead of the previous Supabase auth context:

```typescript
import { useAuth } from "@/contexts/auth/FirebaseAuthContext";
```

### 4. Environment Variables

Required environment variables:

```env
# Firebase Configuration
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
VITE_FIREBASE_PROJECT_ID=your_firebase_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
VITE_FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
VITE_FIREBASE_APP_ID=your_firebase_app_id

# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## Migration Steps Applied

1. **Database Migration**: Run the comprehensive Firebase compatibility migration
2. **Update Auth Context**: Switch from Supabase auth to Firebase auth
3. **Update Service Layer**: Use service role key for database operations
4. **Deploy Changes**: Update environment variables and deploy

## Important Considerations

### Security
- The application now relies on the service role key for all database operations
- Authorization must be implemented at the application level
- Never expose the service role key to the client

### User Sync
- When users authenticate with Firebase, their profile is synced to `user_profiles` table
- The sync happens in `firebaseAuth.ts` via `syncUserToSupabase()`

### Notifications
- The notification service uses the service role key to bypass RLS
- This is handled in `NotificationService.ts`

## Troubleshooting

### Common Issues

1. **"invalid input syntax for type uuid"**
   - Cause: Database still expects UUID format
   - Solution: Run the Firebase compatibility migration

2. **"Auth session missing"**
   - Cause: Trying to use Supabase auth instead of Firebase
   - Solution: Ensure using Firebase auth context

3. **"Service role key not configured"**
   - Cause: Missing `VITE_SUPABASE_SERVICE_ROLE_KEY`
   - Solution: Add the service role key to environment variables

## Migration Status

✅ **Migration Completed Successfully** (January 2025)
- Database schema updated to support Firebase user IDs
- All RLS policies replaced with service role access
- Notifications system fully functional
- Application deployed and tested in production

## Future Considerations

- Consider implementing a custom RLS solution if needed
- Monitor performance as all queries now use service role access
- Regularly audit authorization logic in the application layer 