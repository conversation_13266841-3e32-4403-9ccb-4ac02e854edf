import { supabase } from '@/lib/supabase';
import { createClient } from '@supabase/supabase-js';

// Create a service client with service role key for bypassing RLS
const getServiceRoleClient = () => {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const serviceRoleKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY || import.meta.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !serviceRoleKey) {
    console.error('Missing Supabase URL or Service Role Key for notifications service');
    return null;
  }
  
  return createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

export interface AppNotification {
  id: string;
  user_id: string;
  story_id?: string | null;
  contribution_id?: string | null;
  actor_id?: string | null;
  type: string;
  content: {
    message: string;
    actor_username?: string;
    story_title?: string;
  };
  is_read: boolean;
  created_at: string;
  read_at?: string | null;
}

export class NotificationService {
  // Fetch notifications for a user (bypasses <PERSON><PERSON> using service role)
  static async fetchNotifications(userId: string): Promise<{
    notifications: AppNotification[];
    unreadCount: number;
  }> {
    try {
      const serviceClient = getServiceRoleClient();
      if (!serviceClient) {
        throw new Error('Service role key not configured. Please add VITE_SUPABASE_SERVICE_ROLE_KEY to environment variables.');
      }

      // Use service role client to bypass RLS
      const { data, error } = await serviceClient
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      const notifications = data?.map(this.parseDbNotification) || [];
      
      // Get unread count
      const { count: unreadCount, error: countError } = await serviceClient
        .from('notifications')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('is_read', false);

      if (countError) {
        console.error('Error fetching unread count:', countError);
      }

      return {
        notifications,
        unreadCount: unreadCount || 0
      };
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw error;
    }
  }

  // Mark notification as read
  static async markAsRead(notificationId: string, userId: string): Promise<AppNotification | null> {
    try {
      const serviceClient = getServiceRoleClient();
      if (!serviceClient) {
        throw new Error('Service role key not configured. Please add VITE_SUPABASE_SERVICE_ROLE_KEY to environment variables.');
      }

      const { data, error } = await serviceClient
        .from('notifications')
        .update({ is_read: true, read_at: new Date().toISOString() })
        .eq('id', notificationId)
        .eq('user_id', userId) // Ensure user can only update their own
        .select()
        .single();

      if (error) throw error;
      return data ? this.parseDbNotification(data) : null;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  // Mark all notifications as read
  static async markAllAsRead(userId: string): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient();
      if (!serviceClient) {
        throw new Error('Service role key not configured. Please add VITE_SUPABASE_SERVICE_ROLE_KEY to environment variables.');
      }

      const { error } = await serviceClient
        .from('notifications')
        .update({ is_read: true, read_at: new Date().toISOString() })
        .eq('user_id', userId)
        .eq('is_read', false);

      if (error) throw error;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  // Parse database notification
  private static parseDbNotification(dbNotification: any): AppNotification {
    let parsedContent = { message: 'Notification content missing.' };
    if (typeof dbNotification.content === 'string') {
      try {
        parsedContent = JSON.parse(dbNotification.content);
      } catch (e) { 
        console.error('Failed to parse notification content string:', e); 
      }
    } else if (typeof dbNotification.content === 'object' && dbNotification.content !== null) {
      parsedContent = dbNotification.content;
    }

    return {
      ...dbNotification,
      content: parsedContent,
    } as AppNotification;
  }

  // Subscribe to real-time notifications (still uses regular client for realtime)
  static subscribeToNotifications(userId: string, callback: (notification: AppNotification) => void) {
    const channel = supabase
      .channel(`notifications-${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`,
        },
        (payload) => {
          console.log('New notification received:', payload);
          const newNotification = this.parseDbNotification(payload.new);
          callback(newNotification);
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log(`Subscribed to new notifications for user ${userId}`);
        } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
          console.error(`Notification subscription error for user ${userId}:`, err);
        }
      });

    return {
      unsubscribe: () => {
        supabase.removeChannel(channel);
      }
    };
  }
} 