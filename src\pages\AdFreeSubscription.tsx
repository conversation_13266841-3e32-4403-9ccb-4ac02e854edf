import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { useAuth } from "@/contexts/auth";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Check, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const AdFreeSubscription = () => {
  const [isProcessing, setIsProcessing] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const plan = {
    price: 1.99,
    billingPeriod: "month",
  };

  const features = [
    "Ad-free experience across the entire platform",
    "Support ongoing development of the platform",
    "Faster page loading times",
    "Cleaner reading experience",
    "Cancel anytime",
  ];

  const handleSubscribe = async () => {
    setIsProcessing(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));
      toast({
        title: "Subscription successful!",
        description:
          "Welcome to the ad-free experience. Enjoy the uninterrupted storytelling!",
      });
      setTimeout(() => {
        navigate("/dashboard");
      }, 1000);
    } catch (error) {
      console.error("Error subscribing:", error);
      toast({
        title: "Subscription failed",
        description:
          "There was a problem processing your subscription. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="max-w-3xl mx-auto py-8">
      <h1 className="text-3xl font-serif mb-2">Ad-Free Experience</h1>
      <p className="text-gray-600 mb-8">
        Enjoy an uninterrupted storytelling experience for just $1.99/month.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Your Ad-Free Plan</CardTitle>
              <CardDescription>
                Just $1.99 per month for an uninterrupted experience.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mt-6">
                <h3 className="font-medium mb-3">What's included:</h3>
                <ul className="space-y-2">
                  {features.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-2" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <Button
                onClick={handleSubscribe}
                className="w-full bg-literary-burgundy hover:bg-literary-burgundy/90"
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>Subscribe for $1.99/month</>
                )}
              </Button>
              <p className="text-xs text-center text-gray-500">
                By subscribing, you agree to our Terms of Service and Privacy
                Policy. You can cancel your subscription at any time.
              </p>
            </CardFooter>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span>Ad-Free Experience (Monthly)</span>
                <span>${plan.price}</span>
              </div>

              <Separator />

              <div className="flex justify-between font-bold">
                <span>Total</span>
                <span>
                  ${plan.price}/{plan.billingPeriod}
                </span>
              </div>

              <div className="text-sm text-gray-500">
                Billed monthly. Cancel anytime.
              </div>
            </CardContent>
          </Card>

          <div className="mt-4 bg-amber-50 border border-amber-200 rounded-lg p-4">
            <h3 className="font-medium text-amber-800 mb-2">
              Money-Back Guarantee
            </h3>
            <p className="text-sm text-amber-700">
              If you're not satisfied with your ad-free experience, contact us
              within 14 days for a full refund.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdFreeSubscription;
