-- Migration: Make notifications table compatible with Firebase Auth IDs

-- Drop existing foreign key constraints and policies
DROP TRIGGER IF EXISTS trigger_new_contribution_notification ON public.contributions;
DROP FUNCTION IF EXISTS public.handle_new_contribution_notification();
DROP POLICY IF EXISTS "Users can read their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update their own notifications (mark as read/unread)" ON public.notifications;

-- Drop indexes
DROP INDEX IF EXISTS idx_notifications_user_id_is_read;
DROP INDEX IF EXISTS idx_notifications_user_id_created_at;

-- Alter the notifications table to use TEXT for user IDs instead of UUID
ALTER TABLE public.notifications 
  ALTER COLUMN user_id TYPE TEXT,
  ALTER COLUMN actor_id TYPE TEXT;

-- Remove foreign key constraints since Firebase IDs don't exist in auth.users
ALTER TABLE public.notifications 
  DROP CONSTRAINT IF EXISTS notifications_user_id_fkey,
  DROP CONSTRAINT IF EXISTS notifications_actor_id_fkey;

-- Recreate indexes with TEXT type
CREATE INDEX idx_notifications_user_id_is_read ON public.notifications(user_id, is_read);
CREATE INDEX idx_notifications_user_id_created_at ON public.notifications(user_id, created_at DESC);

-- Recreate RLS policies without auth.uid() since we're using Firebase
-- These policies will need to be enforced at the application level instead
CREATE POLICY "Service role can manage all notifications"
ON public.notifications FOR ALL 
USING (true)
WITH CHECK (true);

-- Note: With Firebase Auth, we can't use Supabase's auth.uid() in RLS policies
-- The application will use the service role key to bypass RLS and handle authorization in the application layer 