import { useState, useCallback } from "react";
import { supabase as db } from "@/lib/supabase";
import { createClient } from '@supabase/supabase-js';
import { User } from "@/contexts/auth/types";
import { BadgeTier } from "@/components/ui/achievement-badge";
import { adminService } from "@/services/adminService";

// Create a service client for profile creation (bypasses RLS)
const getServiceRoleClient = () => {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const serviceRoleKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY || import.meta.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !serviceRoleKey) {
    console.error('Missing Supabase URL or Service Role Key for profile creation');
    return null;
  }

  return createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

export const useUserProfile = () => {
  const [user, setUser] = useState<User | null>(null);

  const loadUserProfile = useCallback(async (userId: string) => {
    try {
      // Load user profile data from your database
      const { data, error } = await db
        .from("user_profiles")
        .select("id, username, avatar_url, is_admin, display_name, terms_accepted")
        .eq("id", userId)
        .single();

      if (error) {
        // If profile doesn't exist (PGRST116), create it
        if (error.code === 'PGRST116') {
          console.log("User profile not found, creating new profile for user:", userId);

          // Use service role client to create profile (bypasses RLS)
          const serviceClient = getServiceRoleClient();
          if (!serviceClient) {
            console.error('Cannot create user profile: Service role key not configured');
            return;
          }

          // Create a new user profile using service role
          const { data: newProfile, error: createError } = await serviceClient
            .from("user_profiles")
            .insert({
              id: userId,
              username: null, // Will be set later
              display_name: null,
              avatar_url: null,
              is_admin: false,
              terms_accepted: false,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select("id, username, avatar_url, is_admin, display_name, terms_accepted")
            .single();

          if (createError) {
            console.error("Error creating user profile:", createError);
            return;
          }

          // Use the newly created profile
          if (newProfile) {
            setUser((prevUser) => {
              if (!prevUser) return null;
              return {
                ...prevUser,
                username: newProfile.username,
                profilePicture: newProfile.avatar_url,
                isAdmin: newProfile.is_admin === true,
                displayName: newProfile.display_name,
                termsAccepted: newProfile.terms_accepted === true,
                tier: "free",
                credits: 10,
                achievements: {
                  storiesCreated: 0,
                  storiesParticipated: 0,
                  wordsContributed: 0,
                  votesReceived: 0,
                  badges: [],
                },
              };
            });
          }
          return;
        }

        console.error("Error loading user profile:", error);
        return;
      }

      if (data) {
        // Update user state with profile data
        setUser((prevUser) => {
          if (!prevUser) return null;
          return {
            ...prevUser,
            username: data.username,
            profilePicture: data.avatar_url,
            isAdmin: data.is_admin === true, // Add admin status
            displayName: data.display_name,
            termsAccepted: data.terms_accepted === true, // Add terms acceptance status
            // Add other profile fields as needed
            tier: "free", // Mock tier value, replace with actual data
            credits: 10, // Mock credits value, replace with actual data
            achievements: {
              storiesCreated: 0,
              storiesParticipated: 0,
              wordsContributed: 0,
              votesReceived: 0,
              badges: [],
            },
          };
        });
      }
    } catch (error) {
      console.error("Failed to load user profile:", error);
    }
  }, []); // No dependencies needed since we're using functional updates

  const updateProfile = useCallback(async (data: Partial<User>) => {
    try {
      if (!user?.id) {
        throw new Error("No user ID available");
      }

      // Prepare the update data for the database
      const updateData: any = {};

      if (data.username !== undefined) updateData.username = data.username;
      if (data.profilePicture !== undefined) updateData.avatar_url = data.profilePicture;
      if (data.termsAccepted !== undefined) updateData.terms_accepted = data.termsAccepted;

      // Update the database
      const { error } = await db
        .from("user_profiles")
        .update(updateData)
        .eq("id", user.id);

      if (error) {
        console.error("Error updating user profile:", error);
        throw error;
      }

      // Update local state
      setUser((prevUser) => {
        if (!prevUser) return null;
        return {
          ...prevUser,
          ...data,
        };
      });

    } catch (error) {
      console.error("Failed to update user profile:", error);
      throw error;
    }
  }, [user?.id]);

  const updateAchievements = useCallback(
    async (achievements: Partial<User["achievements"]>) => {
      // Implementation placeholder
    },
    [],
  );

  const addBadge = useCallback(async (badge: BadgeTier) => {
    // Implementation placeholder
  }, []);

  const isAdmin = useCallback(() => {
    // Check if user has admin flag loaded from profile
    return user?.isAdmin === true;
  }, [user?.isAdmin]);

  return {
    user,
    setUser,
    loadUserProfile,
    updateProfile,
    updateAchievements,
    addBadge,
    isAdmin,
  };
};
